id,severity,area,path,evidence,impact,likelihood,fix_steps,owner,eta
R001,P0,Security,landing_page.html,"CSP violation: fonts.googleapis.com","XSS attacks possible via external CDN","High","Remove external fonts or update CSP policy","Dev Team","1 week"
R002,P0,Security,cbcs_demo.html,"Multiple innerHTML injections with user data","XSS attacks via malicious user input","High","Replace innerHTML with textContent or sanitize inputs","Dev Team","1 week"
R003,P1,Security,landing_page.html,"Authentication state in localStorage","Session hijacking via XSS","Medium","Implement httpOnly cookies or secure session management","Dev Team","2 weeks"
R004,P1,Performance,cbcs_demo.html,"2142-line monolithic file","Poor maintainability and load performance","High","Split into modular components","Dev Team","3 weeks"
R005,P1,Security,emergency_intake.html,"Missing CSP header","XSS attacks possible","Medium","Add Content Security Policy header","Dev Team","1 week"
R006,P1,CI/CD,.github/workflows/pages.yml,"Missing verification scripts with fallback","Broken deployments may go undetected","High","Implement linkcheck.cjs and podcheck.cjs","DevOps","1 week"
R007,P1,Quality,wizards/,"Multiple competing wizard implementations","Code duplication and maintenance burden","High","Consolidate to single wizard architecture","Dev Team","6 weeks"
R008,P2,Security,cbcs_demo.html,"Sensitive data in localStorage","Data exposure via client-side storage","Medium","Implement secure data storage or encryption","Dev Team","2 weeks"
R009,P2,Performance,landing_page.html,"Large inline CSS blocks (800+ lines)","Poor caching and load performance","High","Extract to external stylesheets","Dev Team","1 week"
R010,P2,Reliability,cbcs_demo.html,"No error boundaries in wizard flows","Application crashes on errors","Medium","Add comprehensive error handling","Dev Team","2 weeks"
R011,P2,Security,scripts/verify_ui_contract.sh,"Shell injection potential in find command","Code execution via malicious filenames","Low","Validate input parameters and escape shell commands","Dev Team","1 week"
R012,P2,Maintainability,emergency_intake.html,"1045-line monolithic file","Poor maintainability","High","Refactor into modular components","Dev Team","3 weeks"
R013,P2,Performance,public/,"Large PDF assets in repository (9MB+)","Repository bloat and slow clones","Medium","Move large assets to external storage or LFS","DevOps","1 week"
R014,P2,Security,.github/workflows/pages.yml,"Git SHA exposed in public version.json","Information disclosure","Low","Consider if SHA exposure is acceptable or hash it","DevOps","1 week"
R015,P2,Quality,wizards/,"No automated testing","Bugs in production","High","Implement unit and integration tests","Dev Team","4 weeks"
R016,P3,Observability,All files,"No structured logging framework","Difficult debugging and monitoring","Medium","Implement centralized logging system","Dev Team","3 weeks"
R017,P3,Performance,All HTML files,"No code splitting or lazy loading","Poor initial load performance","Medium","Implement modern build system with code splitting","Dev Team","6 weeks"
R018,P3,Security,All files,"No dependency vulnerability scanning","Vulnerable dependencies may be used","Medium","Add automated dependency scanning to CI/CD","DevOps","2 weeks"
R019,P3,Accessibility,All HTML files,"No accessibility testing","Poor user experience for disabled users","Medium","Add automated accessibility testing","Dev Team","3 weeks"
R020,P3,Performance,All files,"No performance monitoring","Performance regressions undetected","Medium","Add Lighthouse CI and performance monitoring","DevOps","2 weeks"
R021,P3,Data,wizards/,"No data validation or schema enforcement","Data corruption and inconsistencies","Medium","Implement JSON schema validation","Dev Team","3 weeks"
R022,P3,Security,All files,"No HTTPS enforcement","Man-in-the-middle attacks possible","Low","Configure HTTPS redirects and HSTS headers","DevOps","1 week"
R023,P3,Reliability,All wizard files,"No state migration strategy","Data loss on application updates","Medium","Implement versioned data migration system","Dev Team","4 weeks"
R024,P3,Quality,All files,"No code quality metrics","Technical debt accumulation","Medium","Add code quality gates and metrics","DevOps","2 weeks"
R025,P3,Security,All files,"No Content Security Policy on most pages","XSS attack surface","Medium","Add CSP headers to all pages","Dev Team","2 weeks"
