file,category,severity,line,evidence,description,fix_recommendation
landing_page.html,csp_violation,P0,9,"fonts.googleapis.com","External CDN violates CSP default-src 'self'","Remove external fonts or update CSP to allow fonts.googleapis.com"
landing_page.html,auth_storage,P1,781,"localStorage.removeItem('isAuthenticated')","Authentication state in localStorage vulnerable to XSS","Use httpOnly cookies or secure session management"
landing_page.html,auth_storage,P1,787,"localStorage.getItem('isAuthenticated')","Authentication check via localStorage","Use secure session validation"
cbcs_demo.html,html_injection,P1,632,"container.innerHTML = uploadedDrawings.map","Direct innerHTML assignment with user data","Use textContent or sanitize HTML content"
cbcs_demo.html,html_injection,P1,666,"progressDiv.innerHTML = `🔍 Analyzing ${drawing.name}...`","Template literal in innerHTML with user input","Sanitize drawing.name or use textContent"
cbcs_demo.html,html_injection,P1,688,"progressDiv.innerHTML = `✅ Analysis complete for ${drawing.name}`","Template literal in innerHTML with user input","Sanitize drawing.name or use textContent"
cbcs_demo.html,html_injection,P1,767,"summaryDiv.innerHTML = `<h3 style=...`","Complex HTML injection via innerHTML","Use DOM methods or sanitize content"
cbcs_demo.html,html_injection,P1,876,"grid.innerHTML = \"\"","innerHTML clearing (low risk)","Use removeChild or replaceChildren"
cbcs_demo.html,html_injection,P1,879,"grid.innerHTML = \"<div style='color:#888;'>...\"","Static HTML injection (medium risk)","Use createElement and textContent"
cbcs_demo.html,html_injection,P1,886,"div.innerHTML = `<input type=\"checkbox\"...`","Template literal with code data","Validate code data or use DOM methods"
cbcs_demo.html,html_injection,P1,1089,"document.getElementById('laborCosts').innerHTML = laborCosts","Function result to innerHTML","Ensure laborCosts function returns safe HTML"
cbcs_demo.html,html_injection,P1,1094,"document.getElementById('materialCosts').innerHTML = materialCosts","Function result to innerHTML","Ensure materialCosts function returns safe HTML"
cbcs_demo.html,html_injection,P1,1099,"document.getElementById('equipmentCosts').innerHTML = equipmentCosts","Function result to innerHTML","Ensure equipmentCosts function returns safe HTML"
cbcs_demo.html,html_injection,P1,1104,"document.getElementById('otherCosts').innerHTML = otherCosts","Function result to innerHTML","Ensure otherCosts function returns safe HTML"
cbcs_demo.html,html_injection,P1,1524,"modal.innerHTML = `<div style=\"background: white;...`","Large HTML template injection","Use template engine or DOM methods"
cbcs_demo.html,html_injection,P1,1598,"mode.innerHTML = '<option value=\"short\">Short</option>...'","Static HTML injection (low risk)","Use createElement for options"
cbcs_demo.html,data_storage,P2,1669,"localStorage.setItem('ComplianceMax_Demo:last_cbcs', JSON.stringify(payload))","Sensitive data in localStorage","Consider sessionStorage or secure storage"
cbcs_demo.html,data_storage,P2,1694,"localStorage.getItem(k)||'null'","localStorage access pattern","Validate and sanitize stored data"
cbcs_demo.html,data_storage,P2,1695,"localStorage.setItem(k, JSON.stringify(v))","localStorage write pattern","Validate data before storage"
cbcs_demo.html,html_injection,P1,1705,"pdfList.innerHTML = '<em>No PDFs uploaded yet.</em>'","Static HTML injection (low risk)","Use textContent for simple text"
cbcs_demo.html,html_injection,P1,1706,"pdfList.innerHTML = items.map(x=>`• ${x.name}...`).join('<br/>')","User data in innerHTML via template","Sanitize x.name or use DOM methods"
cbcs_demo.html,data_storage,P2,1763,"JSON.parse(localStorage.getItem(KEY))","localStorage parsing without validation","Add try-catch and validation"
cbcs_demo.html,data_storage,P2,1764,"localStorage.setItem(KEY, JSON.stringify(all))","localStorage write without validation","Validate data structure before storage"
cbcs_demo.html,data_storage,P2,1783,"localStorage.setItem(\"cmx:lastProjectId\", id)","Project ID in localStorage","Consider security implications"
cbcs_demo.html,data_storage,P2,1787,"localStorage.getItem(\"cmx:lastProjectId\")","Project ID retrieval","Validate project ID exists and user has access"
cbcs_demo.html,data_storage,P2,1798,"JSON.parse(localStorage.getItem(actKey(id)))","Activity data parsing","Add validation and error handling"
cbcs_demo.html,data_storage,P2,1800,"localStorage.setItem(actKey(id), JSON.stringify(arr.slice(0, 100)))","Activity data storage","Consider data retention policies"
cbcs_demo.html,data_storage,P2,1937,"localStorage.getItem('cmx:lastProjectId')","Project ID in onclick handler","Validate project access in download function"
emergency_intake.html,missing_csp,P1,1,"No CSP header found","Missing Content Security Policy","Add CSP header to prevent XSS"
worksheet.html,missing_csp,P1,1,"No CSP header found","Missing Content Security Policy","Add CSP header to prevent XSS"
dashboard.html,missing_csp,P1,1,"No CSP header found","Missing Content Security Policy","Add CSP header to prevent XSS"
ui/nav.css,external_fonts,P2,1,"@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap')","External font import in CSS","Use local fonts or update CSP"
scripts/verify_ui_contract.sh,shell_injection,P2,4,"find . -maxdepth 1 -type f -name '*.html'","Shell command with user input","Validate input parameters"
.github/workflows/pages.yml,secrets_exposure,P2,32,"echo \"{\\\"sha\\\":\\\"$GITHUB_SHA\\\",\\\"built\\\":\\\"$(date -u +%FT%TZ)\\\"}\" > version.json","Git SHA in public file","Consider if SHA exposure is acceptable"
