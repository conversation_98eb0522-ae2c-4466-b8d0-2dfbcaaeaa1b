File_Type,Count,Security_Relevance,Notes
.js,8497,<PERSON><PERSON><PERSON>,JavaScript files - potential XSS vectors
.map,3492,LOW,Source maps - development artifacts
.ts,2190,HIGH,TypeScript files - need security review
.py,1503,<PERSON><PERSON><PERSON>,Python files - server-side security
.md,1281,<PERSON><PERSON>,Markdown documentation
.h,1230,<PERSON>OW,C header files - likely dependencies
.json,1029,ME<PERSON>UM,Configuration files - may contain secrets
.pyc,993,<PERSON><PERSON>,Python bytecode - compiled artifacts
.txt,763,LOW,Text files - documentation
(no extension),677,MEDIUM,Unknown file types - need investigation
.html,646,CRITICAL,HTML files - XSS and CSP concerns
.tsx,630,CRITICAL,React TypeScript - dangerouslySetInnerHTML risk
.ini,444,<PERSON>OW,Configuration files
.gz,384,LOW,Compressed archives
.pdf,292,LOW,Documentation files
.htm,146,CRITICAL,HTML files - same risks as .html
.cjs,96,HIGH,CommonJS modules - security review needed
.csv,88,<PERSON><PERSON>,Data files
.mjs,87,<PERSON><PERSON>H,ES modules - security review needed
.yml,81,<PERSON><PERSON><PERSON>,YAML configuration - may contain secrets
.jst,75,<PERSON><PERSON><PERSON>,<PERSON>mplate files - potential injection risk
.cts,69,HIGH,TypeScript configuration
.ps1,69,MEDIUM,PowerShell scripts - execution risk
.mts,66,HIGH,TypeScript modules
.cmd,66,MEDIUM,Command files - execution risk
.tsx__tmpl__,54,HIGH,TypeScript templates - injection risk
.c,48,LOW,C source files - likely dependencies
.jsx,36,HIGH,React JavaScript - XSS risk
.css,36,MEDIUM,Stylesheets - CSS injection risk
.pyi,24,LOW,Python type stubs
.pptx,20,LOW,PowerPoint files
.def,15,LOW,Definition files
.sample,14,LOW,Sample files
.pyx,12,LOW,Cython files
.xlsx,12,LOW,Excel files
.markdown,12,LOW,Markdown files
.rss,12,LOW,RSS feeds
.jsx__tmpl__,12,HIGH,React templates - injection risk
.docx,11,LOW,Word documents
.bnf,9,LOW,Grammar files
.sql,9,MEDIUM,SQL files - injection risk
.sh,9,MEDIUM,Shell scripts - execution risk
.flow,9,LOW,Flow type files
.log,7,LOW,Log files
.yaml,6,MEDIUM,YAML files - configuration risk
.applescript,6,MEDIUM,AppleScript files - execution risk
.babelrc,6,LOW,Babel configuration
.npmignore,6,LOW,NPM ignore files
.BSD,6,LOW,License files
.build,6,LOW,Build files
.pxd,6,LOW,Cython definition files
.pxi,6,LOW,Cython include files
.download,6,LOW,Download files
.gitignore,5,LOW,Git ignore files
.zip,4,LOW,Archive files
.diff,3,LOW,Diff files
.closure-compiler,3,LOW,Compiler files
.exe,3,HIGH,Executable files - security risk
.php,3,HIGH,PHP files - server-side security
.nojekyll,3,LOW,Jekyll configuration
.tar,3,LOW,Archive files
.gitattributes,3,LOW,Git attributes
.bat,3,MEDIUM,Batch files - execution risk
.jshintrc,3,LOW,JSHint configuration
.1,3,LOW,Manual pages
.lock,3,LOW,Lock files
.esprima,3,LOW,Parser files
.mako,3,MEDIUM,Template files - injection risk
.rev,1,LOW,Revision files
.pack,1,LOW,Pack files
.idx,1,LOW,Index files

SUMMARY:
Total Files: 25,847
High Security Risk: 11,449 files (.js, .ts, .tsx, .jsx, .html, .htm, .cjs, .mjs, etc.)
Critical Security Risk: 1,276 files (.html, .htm, .tsx)
Medium Security Risk: 2,156 files (.json, .py, .yml, .sql, etc.)
Low Security Risk: 10,966 files (documentation, archives, etc.)

IMMEDIATE ATTENTION REQUIRED:
- 646 HTML files need CSP header review
- 630 TSX files need dangerouslySetInnerHTML audit
- 8,497 JS files need XSS vulnerability scan
- 1,029 JSON files need secret scanning
